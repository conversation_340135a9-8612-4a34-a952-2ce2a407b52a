import { WorkExperience } from "../utils/types";

export const workExperience: WorkExperience[] = [
	{
		company: "TechCorp Inc.",
		location: "San Francisco, CA (Remote)",
		role: "Senior Full Stack Developer",
		period: "March 2024 - Present",
		type: "Full-time",
		description:
			"Leading the development of scalable web applications and mentoring junior developers.",
		achievements: [
			"Designed and implemented scalable, responsive web applications, reducing load time by 40%.",
			"Created optimized user interfaces that led to a 25% increase in customer retention.",
			"Collaborated with cross-functional teams to improve delivery timelines by 30%.",
			"Mentored 3 junior developers and established code review best practices.",
		],
		technologies: ["React", "Next.js", "TypeScript", "Node.js", "PostgreSQL", "AWS"],
	},
	{
		company: "StartupXYZ",
		location: "New York, NY",
		role: "Full Stack Developer",
		period: "June 2022 - February 2024",
		type: "Full-time",
		description: "Built the core platform from scratch and scaled it to serve thousands of users.",
		achievements: [
			"Developed the entire frontend and backend architecture from ground up.",
			"Implemented real-time features using WebSocket technology.",
			"Optimized database queries resulting in 50% faster response times.",
			"Built responsive designs that work seamlessly across all devices.",
		],
		technologies: ["React", "Express.js", "MongoDB", "Socket.io", "Stripe", "Docker"],
	},
];
