import { SiteConfig } from './types';
import { socialLinks } from './personal-info';

// Site Configuration
export const siteConfig: SiteConfig = {
	name: "<PERSON><PERSON><PERSON><PERSON><PERSON> Swarnkar",
	description: "Full Stack Developer & Open Source Contributor",
	url: "https://yourportfolio.com", // Replace with actual URL
	ogImage: "/og-image.jpg", // Add when available
	links: socialLinks
};

// Navigation Configuration
export const navigation = [
	{ title: "Home", href: "/" },
	{ title: "Projects", href: "/projects" },
	{ title: "Blog", href: "/blog" },
	{ title: "Work", href: "/work" },
	{ title: "About", href: "/about" },
	{ title: "Contact", href: "/contact" }
];

// Animation Delays
export const animationDelays = {
	card: 100, // milliseconds between card animations
	section: 200, // milliseconds between section animations
	item: 50 // milliseconds between list item animations
};

// Pagination
export const pagination = {
	projectsPerPage: 9,
	blogPostsPerPage: 6,
	workExperiencePerPage: 10
};

// Image Configurations
export const imageConfig = {
	placeholder: "/placeholder.svg",
	userPlaceholder: "/placeholder-user.jpg",
	logoPlaceholder: "/placeholder-logo.svg",
	defaultOgImage: "/og-image.jpg"
};

// External URLs
export const externalUrls = {
	github: "https://github.com",
	linkedin: "https://linkedin.com",
	twitter: "https://twitter.com",
	unsplash: "https://images.unsplash.com"
};

// API Endpoints (for future use)
export const apiEndpoints = {
	projects: "/api/projects",
	blogPosts: "/api/blog",
	workExperience: "/api/work",
	contact: "/api/contact"
};

// Form Validation
export const validation = {
	email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
	phone: /^\+?[\d\s\-\(\)]+$/,
	minPasswordLength: 8,
	maxMessageLength: 1000
};

// Theme Configuration
export const themeConfig = {
	defaultTheme: 'dark' as const,
	themes: ['light', 'dark', 'system'] as const
};

// SEO Configuration
export const seoConfig = {
	defaultTitle: siteConfig.name,
	titleTemplate: `%s | ${siteConfig.name}`,
	defaultDescription: siteConfig.description,
	keywords: [
		"full stack developer",
		"react developer",
		"next.js developer",
		"typescript",
		"web development",
		"frontend developer",
		"backend developer",
		"javascript",
		"portfolio"
	]
};

// Social Media
export const socialMedia = {
	github: {
		name: "GitHub",
		icon: "Github",
		url: socialLinks.github || ""
	},
	linkedin: {
		name: "LinkedIn",
		icon: "Linkedin",
		url: socialLinks.linkedin || ""
	},
	twitter: {
		name: "Twitter",
		icon: "Twitter",
		url: socialLinks.twitter || ""
	},
	email: {
		name: "Email",
		icon: "Mail",
		url: socialLinks.email || ""
	}
};

// Project Categories
export const projectCategories = [
	"All",
	"Web Development",
	"Mobile Development",
	"E-commerce",
	"Dashboard",
	"API",
	"Open Source"
];

// Blog Categories
export const blogCategories = [
	"All",
	"Web Development",
	"React",
	"Next.js",
	"TypeScript",
	"JavaScript",
	"Tutorial",
	"Tips & Tricks"
];

// Technology Categories
export const technologyCategories = {
	frontend: ["React", "Next.js", "Vue.js", "TypeScript", "JavaScript", "Tailwind CSS", "CSS", "HTML"],
	backend: ["Node.js", "Express.js", "Python", "Django", "FastAPI", "PHP", "Laravel"],
	database: ["PostgreSQL", "MongoDB", "MySQL", "Redis", "Firebase"],
	tools: ["Git", "Docker", "AWS", "Vercel", "Netlify", "Figma", "VS Code"],
	mobile: ["React Native", "Flutter", "Swift", "Kotlin"]
};

// Contact Form Configuration
export const contactFormConfig = {
	maxNameLength: 100,
	maxSubjectLength: 200,
	maxMessageLength: 1000,
	requiredFields: ["name", "email", "message"],
	emailService: "emailjs", // or "nodemailer", "sendgrid", etc.
};

// Feature Flags (for future features)
export const featureFlags = {
	enableBlog: true,
	enableContact: true,
	enableDarkMode: true,
	enableAnimations: true,
	enableAnalytics: false,
	enableComments: false
};
