import type React from "react";
import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Navigation } from "@/components/navigation";
import { Suspense } from "react";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
	title: "<PERSON> Johnson - Full Stack Developer",
	description:
		"<PERSON><PERSON><PERSON> of <PERSON>, a passionate Full Stack Developer specializing in React, Next.js, and modern web technologies.",
	generator: "v0.dev",
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
	return (
		<html lang="en">
			<body className={inter.className}>
				<Navigation />
				<Suspense fallback={<div className="min-h-screen bg-black animate-pulse" />}>
					<main className="transition-opacity duration-300 ease-in-out pt-24">{children}</main>
				</Suspense>
			</body>
		</html>
	);
}
