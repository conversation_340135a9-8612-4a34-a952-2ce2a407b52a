// Core Data Types
export interface Project {
	id: number;
	title: string;
	description: string;
	image: string;
	technologies: string[];
	githubUrl: string;
	liveUrl: string;
	featured?: boolean;
	category?: string;
	status?: 'completed' | 'in-progress' | 'planned';
}

export interface BlogPost {
	id: string;
	title: string;
	description: string;
	image: string;
	tags: string[];
	date: string;
	readTime: string;
	content: string;
	author?: string;
	featured?: boolean;
	published?: boolean;
}

export interface WorkExperience {
	company: string;
	location: string;
	role: string;
	period: string;
	type: string;
	description: string;
	achievements: string[];
	technologies: string[];
	logo?: string;
	website?: string;
}

// Personal Information Types
export interface PersonalInfo {
	name: string;
	title: string;
	description: string;
	email: string;
	location: string;
	avatar: string;
	resume?: string;
	availability: {
		status: 'available' | 'busy' | 'unavailable';
		message: string;
	};
}

export interface SocialLinks {
	github?: string;
	linkedin?: string;
	twitter?: string;
	instagram?: string;
	website?: string;
	email?: string;
}

export interface Skill {
	name: string;
	level: number;
	category: 'frontend' | 'backend' | 'database' | 'tools' | 'other';
}

// UI Component Types
export interface CardProps {
	type: "project" | "blog";
	title: string;
	description: string;
	image: string;
	link: string;
	tags: string[];
	date?: string;
	readTime?: string;
	githubUrl?: string;
	liveUrl?: string;
	index: number;
}

// Filter and Search Types
export interface FilterOptions {
	searchQuery: string;
	selectedTags: string[];
	category?: string;
	sortBy?: 'date' | 'title' | 'popularity';
	sortOrder?: 'asc' | 'desc';
}

// API Response Types
export interface ApiResponse<T> {
	data: T;
	success: boolean;
	message?: string;
	error?: string;
}

// Configuration Types
export interface SiteConfig {
	name: string;
	description: string;
	url: string;
	ogImage: string;
	links: SocialLinks;
}

// Navigation Types
export interface NavItem {
	title: string;
	href: string;
	disabled?: boolean;
	external?: boolean;
}

// Theme Types
export type Theme = 'light' | 'dark' | 'system';

// Form Types
export interface ContactForm {
	name: string;
	email: string;
	subject: string;
	message: string;
}

// Utility Types
export type WithId<T> = T & { id: string | number };
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
