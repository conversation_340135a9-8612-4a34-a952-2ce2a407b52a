import { PersonalInfo, SocialLinks, Skill } from './types';

export const personalInfo: PersonalInfo = {
	name: "<PERSON><PERSON><PERSON><PERSON><PERSON> Swarnkar",
	title: "Full Stack Developer",
	description: "I'm a Full Stack web developer and Open Source Contributor, with a focus on JavaScript, React, and UI/UX design. Enthusiastic about Three.js, driven by a keen eye for design.",
	email: "<EMAIL>", // Replace with actual email
	location: "San Francisco, CA",
	avatar: "/placeholder-user.jpg",
	resume: "/resume.pdf", // Add when available
	availability: {
		status: "available",
		message: "Available for new projects"
	}
};

export const socialLinks: SocialLinks = {
	github: "https://github.com/yourusername", // Replace with actual GitHub
	linkedin: "https://linkedin.com/in/yourusername", // Replace with actual LinkedIn
	twitter: "https://twitter.com/yourusername", // Replace with actual Twitter
	email: "mailto:ramk<PERSON><EMAIL>", // Replace with actual email
	website: "https://yourportfolio.com" // Replace with actual website
};

export const skills: Skill[] = [
	{ name: "Frontend Development", level: 90, category: "frontend" },
	{ name: "Backend Development", level: 85, category: "backend" },
	{ name: "UI/UX Design", level: 80, category: "frontend" },
	{ name: "Database Design", level: 75, category: "database" },
	{ name: "React", level: 95, category: "frontend" },
	{ name: "Next.js", level: 90, category: "frontend" },
	{ name: "TypeScript", level: 88, category: "frontend" },
	{ name: "Node.js", level: 85, category: "backend" },
	{ name: "PostgreSQL", level: 80, category: "database" },
	{ name: "MongoDB", level: 75, category: "database" },
	{ name: "Three.js", level: 70, category: "frontend" },
	{ name: "GraphQL", level: 75, category: "backend" },
	{ name: "AWS", level: 70, category: "tools" },
	{ name: "Docker", level: 65, category: "tools" }
];

export const techStack: string[] = [
	"JavaScript",
	"TypeScript",
	"React",
	"Next.js",
	"Node.js",
	"Tailwind CSS",
	"PostgreSQL",
	"MongoDB",
	"GraphQL",
	"Three.js",
];

// Professional summary sections
export const professionalSummary = {
	yearsOfExperience: 3,
	projectsCompleted: 50,
	clientsSatisfied: 25,
	technologiesMastered: techStack.length
};

// About page content
export const aboutContent = {
	introduction: "Hello! I'm Ramkrishna, a passionate full-stack developer with a love for creating beautiful, functional web applications.",
	background: "With over 3 years of experience in web development, I've had the opportunity to work on diverse projects ranging from e-commerce platforms to real-time applications.",
	passion: "I'm particularly passionate about modern web technologies, user experience design, and the intersection of creativity and code.",
	currentFocus: "Currently, I'm focused on mastering Three.js and exploring the possibilities of 3D web experiences.",
	values: [
		"Clean, maintainable code",
		"User-centered design",
		"Continuous learning",
		"Open source contribution",
		"Team collaboration"
	]
};

// Contact information
export const contactInfo = {
	email: personalInfo.email,
	phone: "+****************", // Replace with actual phone
	location: personalInfo.location,
	timezone: "PST (UTC-8)",
	preferredContact: "email" as const,
	responseTime: "Within 24 hours"
};
