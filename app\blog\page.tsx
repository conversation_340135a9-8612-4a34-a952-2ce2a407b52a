"use client";

import Link from "next/link";
import { ArrowRight } from "lucide-react";
import Image from "next/image";
import Card from "../components/card";
import { DataService } from "../utils";

export default function BlogPage() {
	const blogPosts = DataService.blog.getAll();

	return (
		<div className="min-h-screen bg-black">
			<div className="max-w-5xl mx-auto px-6 py-20">
				{/* Header */}
				<div className="mb-16 text-center">
					<h1 className="text-4xl font-bold text-white mb-4">Blog</h1>
					<p className="text-zinc-400 text-lg max-w-2xl mx-auto">
						Thoughts, tutorials, and insights about web development and technology.
					</p>
				</div>

				{/* Blog Posts Grid */}
				<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
					{blogPosts.map((post, index) => (
						<Card
							key={post.id}
							type="blog"
							title={post.title}
							description={post.description}
							image={post.image}
							link={`/blog/${post.id}`}
							tags={post.tags}
							date={post.date}
							readTime={post.readTime}
							index={index}
						/>
					))}
				</div>
			</div>
		</div>
	);
}
