import Image from "next/image";
import Link from "next/link";
import { <PERSON>R<PERSON>, Github, ExternalLink, Calendar, Clock } from "lucide-react";
import clsx from "clsx";

interface CardProps {
	type: "project" | "blog";
	title: string;
	description: string;
	image: string;
	link: string;
	tags: string[];
	date?: string;
	readTime?: string;
	githubUrl?: string;
	liveUrl?: string;
	index: number;
}

function getDomain(url?: string) {
	if (!url) return "";
	try {
		const { hostname } = new URL(url);
		return hostname.replace(/^www\./, "");
	} catch {
		return url;
	}
}

export default function Card({
	type,
	title,
	description,
	image,
	link,
	tags,
	date,
	readTime,
	githubUrl,
	liveUrl,
	index,
}: CardProps) {
	if (type === "blog") {
		// Blog: whole card is a link, shadow on hover, no scale
		return (
			<Link
				href={link}
				className={clsx(
					"group relative h-64 rounded-2xl overflow-hidden cursor-pointer transition-all duration-300",
					"bg-black border border-zinc-800 hover:shadow-2xl hover:shadow-blue-900/30"
				)}
				style={{ animationDelay: `${index * 100}ms` }}
			>
				{/* Background Image */}
				<div className="absolute inset-0">
					<Image
						src={image || "/placeholder.svg"}
						alt={title}
						width={400}
						height={400}
						className="w-full h-full object-cover transition-transform duration-700"
					/>
					{/* Overlay */}
					<div className="absolute inset-0 bg-gradient-to-t from-black via-black/40 to-transparent opacity-90 transition-opacity duration-300"></div>
				</div>
				{/* Content */}
				<div className="absolute bottom-0 left-0 right-0 p-5">
					{/* Meta Info */}
					{date && readTime && (
						<div className="flex items-center gap-3 text-xs text-zinc-400 mb-2 opacity-80">
							<div className="flex items-center gap-1">
								<Calendar className="h-3 w-3" />
								{date}
							</div>
							<div className="flex items-center gap-1">
								<Clock className="h-3 w-3" />
								{readTime}
							</div>
						</div>
					)}
					<h3 className="font-semibold text-lg text-white mb-1 group-hover:text-blue-400 transition-colors duration-300 line-clamp-2">
						{title}
					</h3>
					<p className="text-zinc-300 text-xs mb-3 opacity-90 line-clamp-2">{description}</p>
					<div className="flex flex-wrap gap-1.5 mb-0 transition-all duration-500">
						{tags.slice(0, 2).map((tag) => (
							<span
								key={tag}
								className="bg-zinc-900/80 backdrop-blur-sm text-zinc-300 text-xs font-medium px-2 py-1 rounded-full border border-zinc-700/50"
							>
								{tag}
							</span>
						))}
						{tags.length > 2 && (
							<span className="bg-zinc-900/80 backdrop-blur-sm text-zinc-400 text-xs font-medium px-2 py-1 rounded-full border border-zinc-700/50">
								+{tags.length - 2}
							</span>
						)}
					</div>
				</div>
			</Link>
		);
	}

	// Project: div container, no scale on hover, only action links are clickable/highlighted
	return (
		<div
			className={clsx(
				"group relative h-64 rounded-2xl overflow-hidden cursor-pointer transition-all duration-300",
				"bg-black border border-zinc-800"
			)}
			style={{ animationDelay: `${index * 100}ms` }}
		>
			{/* Background Image */}
			<div className="absolute inset-0">
				<Image
					src={image || "/placeholder.svg"}
					alt={title}
					width={400}
					height={400}
					className="w-full h-full object-cover transition-all duration-300 shadow-lg group-hover:shadow-none"
				/>
				{/* Overlay */}
				<div className="absolute inset-0 bg-gradient-to-t from-black via-black/40 to-transparent opacity-90 transition-opacity duration-300"></div>
			</div>
			{/* Content */}
			<div className="absolute bottom-0 left-0 right-0 p-5">
				<h3 className="font-semibold text-lg text-white mb-1 group-hover:text-blue-400 transition-colors duration-300 line-clamp-2">
					{title}
				</h3>
				<p className="text-zinc-300 text-xs mb-3 opacity-90 line-clamp-2">{description}</p>
				<div className="flex flex-wrap gap-1.5 mb-2 transition-all duration-500">
					{tags.slice(0, 3).map((tag) => (
						<span
							key={tag}
							className="bg-zinc-900/80 backdrop-blur-sm text-zinc-300 text-xs font-medium px-2 py-1 rounded-full border border-zinc-700/50"
						>
							{tag}
						</span>
					))}
					{tags.length > 3 && (
						<span className="bg-zinc-900/80 backdrop-blur-sm text-zinc-400 text-xs font-medium px-2 py-1 rounded-full border border-zinc-700/50">
							+{tags.length - 3}
						</span>
					)}
				</div>
				{/* Action Links */}
				<div className="flex gap-2 mt-2 items-center">
					<a
						href={githubUrl || "#"}
						target="_blank"
						rel="noopener noreferrer"
						className="flex items-center gap-1.5 text-zinc-300 hover:text-white text-xs font-medium transition-all duration-300 bg-zinc-900/90 backdrop-blur-sm px-3 py-1.5 rounded-lg border border-zinc-700/50 hover:border-zinc-600 hover:bg-zinc-800/80 hover:scale-105"
					>
						<Github className="h-3 w-3" />
					</a>
					{liveUrl && (
						<a
							href={liveUrl}
							target="_blank"
							rel="noopener noreferrer"
							className="flex items-center gap-1.5 text-zinc-400 hover:text-blue-400 text-xs font-medium transition-all duration-300 underline underline-offset-2"
						>
							{getDomain(liveUrl)}
							<ExternalLink className="h-3 w-3" />
						</a>
					)}
				</div>
			</div>
		</div>
	);
}
