"use client";

import { Search, X } from "lucide-react";
import { useState, useEffect } from "react";

interface SearchAndFilterProps {
	allTags: string[];
	onSearch: (query: string) => void;
	onTagsChange: (tags: string[]) => void;
	placeholder?: string;
}

export default function SearchAndFilter({
	allTags,
	onSearch,
	onTagsChange,
	placeholder = "Search...",
}: SearchAndFilterProps) {
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedTags, setSelectedTags] = useState<string[]>([]);
	const [isFilterOpen, setIsFilterOpen] = useState(false);

	useEffect(() => {
		onSearch(searchQuery);
	}, [searchQuery, onSearch]);

	useEffect(() => {
		onTagsChange(selectedTags);
	}, [selectedTags, onTagsChange]);

	const toggleTag = (tag: string) => {
		setSelectedTags((prev) =>
			prev.includes(tag) ? prev.filter((t) => t !== tag) : [...prev, tag]
		);
	};

	return (
		<div className="mb-8">
			{/* Search Bar */}
			<div className="relative mb-4">
				<Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-zinc-400" />
				<input
					type="text"
					value={searchQuery}
					onChange={(e) => setSearchQuery(e.target.value)}
					placeholder={placeholder}
					className="w-full bg-zinc-900/50 border border-zinc-800 rounded-xl pl-10 pr-4 py-2.5 text-white placeholder-zinc-500 focus:outline-none focus:border-zinc-700 transition-colors"
				/>
				{searchQuery && (
					<button
						onClick={() => setSearchQuery("")}
						className="absolute right-3 top-1/2 -translate-y-1/2 text-zinc-400 hover:text-zinc-300"
					>
						<X className="h-5 w-5" />
					</button>
				)}
			</div>

			{/* Filter Button */}
			<button
				onClick={() => setIsFilterOpen(!isFilterOpen)}
				className="flex items-center gap-2 text-zinc-400 hover:text-zinc-300 transition-colors"
			>
				<span className="text-sm font-medium">
					{selectedTags.length > 0
						? `${selectedTags.length} tag${selectedTags.length === 1 ? "" : "s"} selected`
						: "Filter by tags"}
				</span>
				<svg
					className={`h-4 w-4 transition-transform ${isFilterOpen ? "rotate-180" : ""}`}
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
				</svg>
			</button>

			{/* Tags Dropdown */}
			{isFilterOpen && (
				<div className="mt-4 p-4 bg-zinc-900/50 border border-zinc-800 rounded-xl">
					<div className="flex flex-wrap gap-2">
						{allTags.map((tag) => (
							<button
								key={tag}
								onClick={() => toggleTag(tag)}
								className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
									selectedTags.includes(tag)
										? "bg-blue-500/20 text-blue-400 border border-blue-500/50"
										: "bg-zinc-800/50 text-zinc-300 border border-zinc-700/50 hover:border-zinc-600"
								}`}
							>
								{tag}
							</button>
						))}
					</div>
				</div>
			)}
		</div>
	);
}
