import { Project, BlogPost, WorkExperience, FilterOptions, ApiResponse } from "./types";
import { projects } from "../data/projects";
import { blogPosts } from "../data/blog-posts";
import { workExperience } from "../data/work-experience";
import { filterProjects, filterBlogPosts } from "./filters";
import { pagination } from "./constants";
import { DataValidator, NotFoundError, ErrorHandler } from "./validation";

// Project Services
export class ProjectService {
	static getAll(): Project[] {
		return projects;
	}

	static getById(id: number): Project | undefined {
		return projects.find((project) => project.id === id);
	}

	static getFeatured(limit?: number): Project[] {
		const featuredProjects = projects.filter((project) => project.featured);
		return limit ? featuredProjects.slice(0, limit) : featuredProjects;
	}

	static getRecent(limit: number = 3): Project[] {
		return projects.slice(0, limit);
	}

	static getByCategory(category: string): Project[] {
		if (category === "All") return projects;
		return projects.filter(
			(project) =>
				project.category === category ||
				project.technologies.some((tech) => tech.toLowerCase().includes(category.toLowerCase()))
		);
	}

	static search(options: FilterOptions): Project[] {
		return filterProjects(projects, options.searchQuery, options.selectedTags);
	}

	static getTechnologies(): string[] {
		const allTech = projects.flatMap((project) => project.technologies);
		return Array.from(new Set(allTech)).sort();
	}

	static getCategories(): string[] {
		const categories = projects
			.map((project) => project.category)
			.filter((category): category is string => Boolean(category));
		return Array.from(new Set(categories)).sort();
	}

	static getPaginated(page: number = 1, perPage: number = pagination.projectsPerPage) {
		const startIndex = (page - 1) * perPage;
		const endIndex = startIndex + perPage;
		const paginatedProjects = projects.slice(startIndex, endIndex);

		return {
			data: paginatedProjects,
			pagination: {
				currentPage: page,
				totalPages: Math.ceil(projects.length / perPage),
				totalItems: projects.length,
				hasNext: endIndex < projects.length,
				hasPrev: page > 1,
			},
		};
	}
}

// Blog Services
export class BlogService {
	static getAll(): BlogPost[] {
		return blogPosts.filter((post) => post.published !== false);
	}

	static getById(id: string): BlogPost | undefined {
		return blogPosts.find((post) => post.id === id && post.published !== false);
	}

	static getFeatured(limit?: number): BlogPost[] {
		const featuredPosts = blogPosts.filter((post) => post.featured && post.published !== false);
		return limit ? featuredPosts.slice(0, limit) : featuredPosts;
	}

	static getRecent(limit: number = 3): BlogPost[] {
		const publishedPosts = blogPosts.filter((post) => post.published !== false);
		return publishedPosts
			.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
			.slice(0, limit);
	}

	static getByTag(tag: string): BlogPost[] {
		return blogPosts.filter((post) => post.tags.includes(tag) && post.published !== false);
	}

	static search(options: FilterOptions): BlogPost[] {
		const publishedPosts = blogPosts.filter((post) => post.published !== false);
		return filterBlogPosts(publishedPosts, options.searchQuery, options.selectedTags);
	}

	static getTags(): string[] {
		const allTags = blogPosts
			.filter((post) => post.published !== false)
			.flatMap((post) => post.tags);
		return Array.from(new Set(allTags)).sort();
	}

	static getPaginated(page: number = 1, perPage: number = pagination.blogPostsPerPage) {
		const publishedPosts = blogPosts.filter((post) => post.published !== false);
		const startIndex = (page - 1) * perPage;
		const endIndex = startIndex + perPage;
		const paginatedPosts = publishedPosts.slice(startIndex, endIndex);

		return {
			data: paginatedPosts,
			pagination: {
				currentPage: page,
				totalPages: Math.ceil(publishedPosts.length / perPage),
				totalItems: publishedPosts.length,
				hasNext: endIndex < publishedPosts.length,
				hasPrev: page > 1,
			},
		};
	}

	static getRelated(currentPostId: string, limit: number = 3): BlogPost[] {
		const currentPost = this.getById(currentPostId);
		if (!currentPost) return [];

		const relatedPosts = blogPosts
			.filter(
				(post) =>
					post.id !== currentPostId &&
					post.published !== false &&
					post.tags.some((tag) => currentPost.tags.includes(tag))
			)
			.sort((a, b) => {
				const aCommonTags = a.tags.filter((tag) => currentPost.tags.includes(tag)).length;
				const bCommonTags = b.tags.filter((tag) => currentPost.tags.includes(tag)).length;
				return bCommonTags - aCommonTags;
			});

		return relatedPosts.slice(0, limit);
	}
}

// Work Experience Services
export class WorkService {
	static getAll(): WorkExperience[] {
		return workExperience;
	}

	static getCurrent(): WorkExperience | undefined {
		return workExperience.find((job) => job.period.toLowerCase().includes("present"));
	}

	static getByCompany(company: string): WorkExperience | undefined {
		return workExperience.find((job) => job.company.toLowerCase().includes(company.toLowerCase()));
	}

	static getTechnologies(): string[] {
		const allTech = workExperience.flatMap((job) => job.technologies);
		return Array.from(new Set(allTech)).sort();
	}

	static getYearsOfExperience(): number {
		// Calculate based on work experience periods
		// This is a simplified calculation - you might want to make it more sophisticated
		return workExperience.length > 0 ? 3 : 0; // Placeholder
	}

	static getPaginated(page: number = 1, perPage: number = pagination.workExperiencePerPage) {
		const startIndex = (page - 1) * perPage;
		const endIndex = startIndex + perPage;
		const paginatedWork = workExperience.slice(startIndex, endIndex);

		return {
			data: paginatedWork,
			pagination: {
				currentPage: page,
				totalPages: Math.ceil(workExperience.length / perPage),
				totalItems: workExperience.length,
				hasNext: endIndex < workExperience.length,
				hasPrev: page > 1,
			},
		};
	}
}

// Combined Data Services
export class DataService {
	static projects = ProjectService;
	static blog = BlogService;
	static work = WorkService;

	// Global search across all content types
	static globalSearch(query: string) {
		const projects = ProjectService.search({ searchQuery: query, selectedTags: [] });
		const blogPosts = BlogService.search({ searchQuery: query, selectedTags: [] });

		return {
			projects,
			blogPosts,
			totalResults: projects.length + blogPosts.length,
		};
	}

	// Get all technologies used across projects and work
	static getAllTechnologies(): string[] {
		const projectTech = ProjectService.getTechnologies();
		const workTech = WorkService.getTechnologies();
		const allTech = [...projectTech, ...workTech];
		return Array.from(new Set(allTech)).sort();
	}
}
