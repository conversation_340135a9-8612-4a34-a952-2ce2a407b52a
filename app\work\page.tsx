import { MapPin, Calendar, Building } from "lucide-react";
import { DataService } from "../utils";

export default function WorkPage() {
	const workExperience = DataService.work.getAll();

	return (
		<div className="min-h-screen bg-black">
			<div className="max-w-4xl mx-auto px-6 py-20">
				{/* Header */}
				<div className="mb-16 text-center">
					<h1 className="text-4xl font-bold text-white mb-4">Work Experience</h1>
					<p className="text-zinc-400 text-lg max-w-2xl mx-auto">
						My professional journey as a developer, showcasing the roles and achievements that have
						shaped my career.
					</p>
				</div>

				{/* Experience Timeline */}
				<div className="space-y-8">
					{workExperience.map((job, index) => (
						<div
							key={index}
							className="bg-gradient-to-br from-zinc-900/50 to-zinc-800/30 border border-zinc-800/50 rounded-2xl p-8 hover:border-zinc-700/50 transition-all duration-500 hover:scale-[1.02] backdrop-blur-sm"
							style={{
								animationDelay: `${index * 200}ms`,
							}}
						>
							<div className="flex flex-col md:flex-row md:justify-between md:items-start mb-6">
								<div className="mb-4 md:mb-0">
									<div className="flex items-center gap-2 mb-2">
										<Building className="h-5 w-5 text-zinc-400" />
										<h3 className="text-xl font-bold text-white">{job.company}</h3>
									</div>
									<h4 className="text-lg font-semibold text-blue-400 mb-2">{job.role}</h4>
									<div className="flex flex-col sm:flex-row gap-2 text-zinc-400 text-sm">
										<div className="flex items-center gap-1">
											<MapPin className="h-4 w-4" />
											{job.location}
										</div>
										<div className="flex items-center gap-1">
											<Calendar className="h-4 w-4" />
											{job.period}
										</div>
									</div>
								</div>
								<span className="bg-zinc-800/50 text-zinc-300 text-xs font-medium px-4 py-2 rounded-full self-start border border-zinc-700/50">
									{job.type}
								</span>
							</div>

							<p className="text-zinc-300 mb-6 leading-relaxed">{job.description}</p>

							<div className="mb-6">
								<h5 className="font-semibold text-white mb-3">Key Achievements:</h5>
								<ul className="space-y-2">
									{job.achievements.map((achievement, i) => (
										<li
											key={i}
											className="text-zinc-300 text-sm flex items-start gap-3 leading-relaxed"
										>
											<div className="w-1.5 h-1.5 bg-zinc-500 rounded-full mt-2 flex-shrink-0"></div>
											{achievement}
										</li>
									))}
								</ul>
							</div>

							<div>
								<h5 className="font-semibold text-white mb-3">Technologies Used:</h5>
								<div className="flex flex-wrap gap-2">
									{job.technologies.map((tech) => (
										<span
											key={tech}
											className="bg-zinc-800/50 text-zinc-300 text-xs font-medium px-3 py-1.5 rounded-full border border-zinc-700/50"
										>
											{tech}
										</span>
									))}
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
}
