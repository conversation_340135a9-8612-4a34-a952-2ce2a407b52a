"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { Github } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";

export function Navigation() {
	const pathname = usePathname();
	const [isScrolled, setIsScrolled] = useState(false);

	useEffect(() => {
		const handleScroll = () => {
			setIsScrolled(window.scrollY > 20);
		};
		window.addEventListener("scroll", handleScroll);
		return () => window.removeEventListener("scroll", handleScroll);
	}, []);

	const navItems = [
		{ href: "/about", label: "About" },
		{ href: "/blog", label: "Blog" },
		{ href: "/projects", label: "Projects" },
		{ href: "/work", label: "Work" },
	];

	return (
		<header
			className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
				isScrolled ? "py-4 bg-black/80 backdrop-blur-lg" : "py-6 bg-transparent"
			}`}
		>
			<div className="max-w-4xl mx-auto px-6">
				<div
					className={`transition-all duration-300 ${
						isScrolled
							? "bg-zinc-900/80 backdrop-blur-sm border border-zinc-800/50 rounded-2xl px-6 py-3"
							: "bg-transparent"
					}`}
				>
					<div className="flex items-center justify-between">
						{/* Logo/Avatar */}
						<Link
							href="/"
							className="flex-shrink-0 transition-transform duration-300 hover:scale-105"
							prefetch={true}
						>
							<div className="w-8 h-8 rounded-full overflow-hidden ring-1 ring-zinc-700 hover:ring-zinc-600 transition-all duration-300">
								<Image
									src="/placeholder.svg?height=32&width=32"
									alt="Profile"
									width={32}
									height={32}
									className="w-full h-full object-cover"
								/>
							</div>
						</Link>

						{/* Navigation */}
						<nav className="hidden md:flex items-center space-x-6">
							{navItems.map((item) => (
								<Link
									key={item.href}
									href={item.href}
									prefetch={true}
									className={`text-sm font-medium transition-all duration-300 hover:text-white relative ${
										pathname === item.href ? "text-white" : "text-zinc-400"
									}`}
								>
									{item.label}
									{pathname === item.href && (
										<div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
									)}
								</Link>
							))}
						</nav>

						{/* GitHub Icon */}
						<Link
							href="https://github.com"
							className="text-zinc-400 hover:text-white transition-all duration-300 hover:scale-110"
							target="_blank"
							rel="noopener noreferrer"
						>
							<Github className="h-4 w-4" />
						</Link>
					</div>
				</div>
			</div>
		</header>
	);
}
