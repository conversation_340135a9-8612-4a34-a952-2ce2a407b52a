import { validation } from './constants';

// Date Utilities
export const dateUtils = {
	formatDate: (dateString: string, format: 'short' | 'long' | 'relative' = 'short'): string => {
		const date = new Date(dateString);
		
		switch (format) {
			case 'short':
				return date.toLocaleDateString('en-US', {
					year: 'numeric',
					month: 'short',
					day: 'numeric'
				});
			case 'long':
				return date.toLocaleDateString('en-US', {
					year: 'numeric',
					month: 'long',
					day: 'numeric'
				});
			case 'relative':
				return dateUtils.getRelativeTime(date);
			default:
				return date.toLocaleDateString();
		}
	},

	getRelativeTime: (date: Date): string => {
		const now = new Date();
		const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
		
		if (diffInSeconds < 60) return 'Just now';
		if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
		if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
		if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
		if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;
		return `${Math.floor(diffInSeconds / 31536000)} years ago`;
	},

	isValidDate: (dateString: string): boolean => {
		const date = new Date(dateString);
		return !isNaN(date.getTime());
	}
};

// String Utilities
export const stringUtils = {
	truncate: (text: string, maxLength: number, suffix: string = '...'): string => {
		if (text.length <= maxLength) return text;
		return text.substring(0, maxLength - suffix.length) + suffix;
	},

	slugify: (text: string): string => {
		return text
			.toLowerCase()
			.replace(/[^\w\s-]/g, '')
			.replace(/[\s_-]+/g, '-')
			.replace(/^-+|-+$/g, '');
	},

	capitalize: (text: string): string => {
		return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
	},

	capitalizeWords: (text: string): string => {
		return text.replace(/\b\w/g, char => char.toUpperCase());
	},

	removeHtml: (html: string): string => {
		return html.replace(/<[^>]*>/g, '');
	},

	extractExcerpt: (content: string, maxLength: number = 150): string => {
		const plainText = stringUtils.removeHtml(content);
		return stringUtils.truncate(plainText, maxLength);
	}
};

// URL Utilities
export const urlUtils = {
	getDomain: (url: string): string => {
		try {
			const { hostname } = new URL(url);
			return hostname.replace(/^www\./, '');
		} catch {
			return url;
		}
	},

	isValidUrl: (url: string): boolean => {
		try {
			new URL(url);
			return true;
		} catch {
			return false;
		}
	},

	addProtocol: (url: string): string => {
		if (!url.startsWith('http://') && !url.startsWith('https://')) {
			return `https://${url}`;
		}
		return url;
	},

	getQueryParams: (url: string): Record<string, string> => {
		try {
			const urlObj = new URL(url);
			const params: Record<string, string> = {};
			urlObj.searchParams.forEach((value, key) => {
				params[key] = value;
			});
			return params;
		} catch {
			return {};
		}
	}
};

// Validation Utilities
export const validationUtils = {
	isValidEmail: (email: string): boolean => {
		return validation.email.test(email);
	},

	isValidPhone: (phone: string): boolean => {
		return validation.phone.test(phone);
	},

	isStrongPassword: (password: string): boolean => {
		return password.length >= validation.minPasswordLength &&
			/[A-Z]/.test(password) &&
			/[a-z]/.test(password) &&
			/\d/.test(password);
	},

	sanitizeInput: (input: string): string => {
		return input.trim().replace(/[<>]/g, '');
	}
};

// Array Utilities
export const arrayUtils = {
	unique: <T>(array: T[]): T[] => {
		return Array.from(new Set(array));
	},

	shuffle: <T>(array: T[]): T[] => {
		const shuffled = [...array];
		for (let i = shuffled.length - 1; i > 0; i--) {
			const j = Math.floor(Math.random() * (i + 1));
			[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
		}
		return shuffled;
	},

	chunk: <T>(array: T[], size: number): T[][] => {
		const chunks: T[][] = [];
		for (let i = 0; i < array.length; i += size) {
			chunks.push(array.slice(i, i + size));
		}
		return chunks;
	},

	groupBy: <T, K extends keyof T>(array: T[], key: K): Record<string, T[]> => {
		return array.reduce((groups, item) => {
			const groupKey = String(item[key]);
			if (!groups[groupKey]) {
				groups[groupKey] = [];
			}
			groups[groupKey].push(item);
			return groups;
		}, {} as Record<string, T[]>);
	}
};

// Number Utilities
export const numberUtils = {
	formatNumber: (num: number): string => {
		return new Intl.NumberFormat().format(num);
	},

	formatCurrency: (amount: number, currency: string = 'USD'): string => {
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency
		}).format(amount);
	},

	clamp: (value: number, min: number, max: number): number => {
		return Math.min(Math.max(value, min), max);
	},

	randomBetween: (min: number, max: number): number => {
		return Math.floor(Math.random() * (max - min + 1)) + min;
	}
};

// Local Storage Utilities
export const storageUtils = {
	set: (key: string, value: any): void => {
		try {
			localStorage.setItem(key, JSON.stringify(value));
		} catch (error) {
			console.warn('Failed to save to localStorage:', error);
		}
	},

	get: <T>(key: string, defaultValue?: T): T | null => {
		try {
			const item = localStorage.getItem(key);
			return item ? JSON.parse(item) : defaultValue || null;
		} catch (error) {
			console.warn('Failed to read from localStorage:', error);
			return defaultValue || null;
		}
	},

	remove: (key: string): void => {
		try {
			localStorage.removeItem(key);
		} catch (error) {
			console.warn('Failed to remove from localStorage:', error);
		}
	},

	clear: (): void => {
		try {
			localStorage.clear();
		} catch (error) {
			console.warn('Failed to clear localStorage:', error);
		}
	}
};

// Performance Utilities
export const performanceUtils = {
	debounce: <T extends (...args: any[]) => any>(
		func: T,
		delay: number
	): (...args: Parameters<T>) => void => {
		let timeoutId: NodeJS.Timeout;
		return (...args: Parameters<T>) => {
			clearTimeout(timeoutId);
			timeoutId = setTimeout(() => func(...args), delay);
		};
	},

	throttle: <T extends (...args: any[]) => any>(
		func: T,
		delay: number
	): (...args: Parameters<T>) => void => {
		let lastCall = 0;
		return (...args: Parameters<T>) => {
			const now = Date.now();
			if (now - lastCall >= delay) {
				lastCall = now;
				func(...args);
			}
		};
	}
};

// SEO Utilities
export const seoUtils = {
	generateMetaTitle: (title: string, siteName: string): string => {
		return `${title} | ${siteName}`;
	},

	generateMetaDescription: (content: string, maxLength: number = 160): string => {
		return stringUtils.truncate(stringUtils.removeHtml(content), maxLength);
	},

	generateKeywords: (tags: string[]): string => {
		return tags.join(', ');
	}
};
