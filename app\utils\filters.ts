import { Project, BlogPost } from "./types";

export function filterProjects(
	projects: Project[],
	searchQuery: string,
	selectedTags: string[]
): Project[] {
	return projects.filter((project) => {
		const matchesSearch = searchQuery
			? project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
			  project.description.toLowerCase().includes(searchQuery.toLowerCase())
			: true;

		const matchesTags =
			selectedTags.length === 0 || selectedTags.every((tag) => project.technologies.includes(tag));

		return matchesSearch && matchesTags;
	});
}

export function filterBlogPosts(
	posts: BlogPost[],
	searchQuery: string,
	selectedTags: string[]
): BlogPost[] {
	return posts.filter((post) => {
		const matchesSearch = searchQuery
			? post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
			  post.description.toLowerCase().includes(searchQuery.toLowerCase())
			: true;

		const matchesTags =
			selectedTags.length === 0 || selectedTags.every((tag) => post.tags.includes(tag));

		return matchesSearch && matchesTags;
	});
}

export function getUniqueTagsFromProjects(projects: Project[]): string[] {
	const allTags = projects.flatMap((project) => project.technologies);
	return Array.from(new Set(allTags)).sort();
}

export function getUniqueTagsFromBlogPosts(posts: BlogPost[]): string[] {
	const allTags = posts.flatMap((post) => post.tags);
	return Array.from(new Set(allTags)).sort();
}

export function getAllTags(items: (Project | BlogPost)[]): string[] {
	const tags = new Set<string>();
	items.forEach((item) => {
		if ("technologies" in item) {
			item.technologies.forEach((tech: any) => tags.add(tech));
		} else if ("tags" in item) {
			item.tags.forEach((tag: any) => tags.add(tag));
		}
	});
	return [...tags].sort();
}
