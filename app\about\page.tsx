import Image from "next/image"
import { MapPin, Calendar } from "lucide-react"

export default function AboutPage() {
  const skills = [
    { category: "Frontend", items: ["React", "Next.js", "TypeScript", "Tailwind CSS", "Three.js"] },
    { category: "Backend", items: ["Node.js", "Express", "Python", "PostgreSQL", "MongoDB"] },
    { category: "Tools", items: ["Git", "Docker", "Vercel", "Figma", "VS Code"] },
  ]

  return (
    <div className="min-h-screen bg-black">
      <div className="max-w-4xl mx-auto px-6 py-20">
        {/* Header */}
        <div className="mb-16 text-center">
          <h1 className="text-4xl font-bold text-white mb-4">About</h1>
          <p className="text-zinc-400 text-lg max-w-2xl mx-auto">
            Get to know more about my journey, skills, and what drives me as a developer.
          </p>
        </div>

        {/* Profile Section */}
        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <div className="space-y-6">
            <div className="w-full h-80 relative rounded-2xl overflow-hidden">
              <Image
                src="/placeholder.svg?height=320&width=400"
                alt="About Me"
                width={400}
                height={320}
                className="object-cover w-full h-full hover:scale-105 transition-transform duration-500"
              />
            </div>
          </div>

          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-white mb-4">My Story</h2>
              <div className="space-y-4 text-zinc-300 leading-relaxed">
                <p>
                  I'm a passionate Full Stack Developer with over 5 years of experience building web applications that
                  solve real-world problems. My journey started with a curiosity about how websites work.
                </p>
                <p>
                  I specialize in modern JavaScript frameworks and have a keen eye for design. I believe that great
                  software is not just functional, but also beautiful and intuitive to use.
                </p>
                <p>
                  When I'm not coding, you'll find me exploring new technologies, contributing to open source projects,
                  or enjoying a good cup of coffee.
                </p>
              </div>
            </div>

            <div className="flex flex-wrap gap-4 text-sm text-zinc-400">
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                San Francisco, CA
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                5+ years experience
              </div>
            </div>
          </div>
        </div>

        {/* Skills Section */}
        <div>
          <h2 className="text-2xl font-bold text-white mb-8 text-center">Skills & Technologies</h2>
          <div className="grid md:grid-cols-3 gap-6">
            {skills.map((skillGroup, index) => (
              <div
                key={skillGroup.category}
                className="bg-gradient-to-br from-zinc-900/50 to-zinc-800/30 border border-zinc-800/50 rounded-2xl p-6 hover:border-zinc-700/50 transition-all duration-500 hover:scale-105 backdrop-blur-sm"
                style={{
                  animationDelay: `${index * 200}ms`,
                }}
              >
                <h3 className="font-semibold text-white mb-4">{skillGroup.category}</h3>
                <div className="flex flex-wrap gap-2">
                  {skillGroup.items.map((skill) => (
                    <span
                      key={skill}
                      className="bg-zinc-800/50 text-zinc-300 text-xs font-medium px-3 py-1.5 rounded-full border border-zinc-700/50 hover:border-zinc-600 transition-all duration-300"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
