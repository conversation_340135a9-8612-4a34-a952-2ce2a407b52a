import { BlogPost } from "../utils/types";

export const blogPosts: BlogPost[] = [
	{
		id: "getting-started-with-nextjs",
		title: "Getting Started with Next.js 15",
		description: "Learn how to build modern web applications with Next.js 15",
		image: "https://images.unsplash.com/photo-1555066931-4365d14bab8c",
		tags: ["Next.js", "React", "Web Development"],
		date: "2024-03-20",
		readTime: "5 min read",
		author: "<PERSON><PERSON><PERSON><PERSON><PERSON>warnkar",
		featured: true,
		published: true,
		content: `# Getting Started with Next.js 15

Next.js 15 is the latest version of the React framework that enables you to build full-stack web applications. In this post, we'll explore the key features and improvements that make Next.js 15 a game-changer for web development.

## What's New in Next.js 15?

Next.js 15 brings several exciting features:

- Improved Server Components
- Enhanced Image Optimization
- Better TypeScript Support
- Turbocharged Development Server

## Server Components

Server Components are a revolutionary feature that allows you to write components that run on the server. This means:

- Reduced client-side JavaScript
- Better performance
- Direct database access
- Improved security

\`\`\`typescript
// Example of a Server Component
async function UserProfile({ userId }: { userId: string }) {
	const user = await db.users.findUnique({ where: { id: userId } })
	return <div>{user.name}</div>
}
\`\`\`

## Getting Started

To create a new Next.js project, run:

\`\`\`bash
npx create-next-app@latest my-app
\`\`\`

This will set up a new project with all the necessary configurations.

## Conclusion

Next.js 15 is a powerful framework that makes building modern web applications easier than ever. With its focus on performance, developer experience, and flexibility, it's the perfect choice for your next project.

Stay tuned for more tutorials and tips on Next.js development!`,
	},
	{
		id: "mastering-react-hooks",
		title: "Mastering React Hooks",
		description: "A comprehensive guide to React Hooks and their practical applications",
		image: "https://images.unsplash.com/photo-1633356122544-f134324a6cee",
		tags: ["React", "JavaScript", "Web Development"],
		date: "2024-03-19",
		readTime: "8 min read",
		author: "Ramkrishna Swarnkar",
		featured: false,
		published: true,
		content: `# Mastering React Hooks

React Hooks have revolutionized how we write React components. In this comprehensive guide, we'll explore the most important hooks and how to use them effectively in your applications.

## Understanding useState

The \`useState\` hook is the most fundamental hook in React. It allows you to add state to functional components:

\`\`\`typescript
import { useState } from 'react'

function Counter() {
	const [count, setCount] = useState(0)
	
	return (
		<div>
			<p>Count: {count}</p>
			<button onClick={() => setCount(count + 1)}>Increment</button>
		</div>
	)
}
\`\`\`

## useEffect for Side Effects

The \`useEffect\` hook is perfect for handling side effects in your components:

\`\`\`typescript
import { useEffect, useState } from 'react'

function DataFetcher() {
	const [data, setData] = useState(null)
	
	useEffect(() => {
		async function fetchData() {
			const response = await fetch('https://api.example.com/data')
			const result = await response.json()
			setData(result)
		}
		
		fetchData()
	}, []) // Empty dependency array means this runs once on mount
	
	return <div>{data ? JSON.stringify(data) : 'Loading...'}</div>
}
\`\`\`

## Custom Hooks

One of the most powerful features of React Hooks is the ability to create custom hooks:

\`\`\`typescript
function useWindowSize() {
	const [size, setSize] = useState({
		width: window.innerWidth,
		height: window.innerHeight
	})
	
	useEffect(() => {
		const handleResize = () => {
			setSize({
				width: window.innerWidth,
				height: window.innerHeight
			})
		}
		
		window.addEventListener('resize', handleResize)
		return () => window.removeEventListener('resize', handleResize)
	}, [])
	
	return size
}
\`\`\`

## Best Practices

1. Always include dependencies in the useEffect dependency array
2. Keep hooks at the top level of your component
3. Use custom hooks to share logic between components
4. Avoid unnecessary re-renders by using useMemo and useCallback

## Conclusion

React Hooks have made it easier than ever to write clean, maintainable React code. By understanding and properly implementing these hooks, you can create more efficient and powerful applications.

Remember to check out the official React documentation for more detailed information about hooks and their usage patterns.`,
	},
];
