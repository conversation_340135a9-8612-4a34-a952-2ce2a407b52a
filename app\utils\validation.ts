import { Project, BlogPost, WorkExperience, ContactForm } from './types';

// Data validation functions
export class DataValidator {
	static validateProject(project: any): project is Project {
		return (
			typeof project === 'object' &&
			project !== null &&
			typeof project.id === 'number' &&
			typeof project.title === 'string' &&
			typeof project.description === 'string' &&
			typeof project.image === 'string' &&
			Array.isArray(project.technologies) &&
			project.technologies.every((tech: any) => typeof tech === 'string') &&
			typeof project.githubUrl === 'string' &&
			typeof project.liveUrl === 'string'
		);
	}

	static validateBlogPost(post: any): post is BlogPost {
		return (
			typeof post === 'object' &&
			post !== null &&
			typeof post.id === 'string' &&
			typeof post.title === 'string' &&
			typeof post.description === 'string' &&
			typeof post.image === 'string' &&
			Array.isArray(post.tags) &&
			post.tags.every((tag: any) => typeof tag === 'string') &&
			typeof post.date === 'string' &&
			typeof post.readTime === 'string' &&
			typeof post.content === 'string'
		);
	}

	static validateWorkExperience(work: any): work is WorkExperience {
		return (
			typeof work === 'object' &&
			work !== null &&
			typeof work.company === 'string' &&
			typeof work.location === 'string' &&
			typeof work.role === 'string' &&
			typeof work.period === 'string' &&
			typeof work.type === 'string' &&
			typeof work.description === 'string' &&
			Array.isArray(work.achievements) &&
			work.achievements.every((achievement: any) => typeof achievement === 'string') &&
			Array.isArray(work.technologies) &&
			work.technologies.every((tech: any) => typeof tech === 'string')
		);
	}

	static validateContactForm(form: any): form is ContactForm {
		return (
			typeof form === 'object' &&
			form !== null &&
			typeof form.name === 'string' &&
			form.name.trim().length > 0 &&
			typeof form.email === 'string' &&
			this.isValidEmail(form.email) &&
			typeof form.subject === 'string' &&
			form.subject.trim().length > 0 &&
			typeof form.message === 'string' &&
			form.message.trim().length > 0
		);
	}

	static isValidEmail(email: string): boolean {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email);
	}

	static isValidUrl(url: string): boolean {
		try {
			new URL(url);
			return true;
		} catch {
			return false;
		}
	}

	static isValidDate(dateString: string): boolean {
		const date = new Date(dateString);
		return !isNaN(date.getTime());
	}

	static sanitizeString(input: string): string {
		return input.trim().replace(/[<>]/g, '');
	}

	static validateSearchQuery(query: string): boolean {
		return typeof query === 'string' && query.length <= 100;
	}

	static validateTags(tags: string[]): boolean {
		return Array.isArray(tags) && tags.every(tag => 
			typeof tag === 'string' && tag.length > 0 && tag.length <= 50
		);
	}
}

// Error classes for better error handling
export class DataError extends Error {
	constructor(message: string, public code: string) {
		super(message);
		this.name = 'DataError';
	}
}

export class ValidationError extends DataError {
	constructor(message: string) {
		super(message, 'VALIDATION_ERROR');
		this.name = 'ValidationError';
	}
}

export class NotFoundError extends DataError {
	constructor(resource: string, id: string | number) {
		super(`${resource} with id "${id}" not found`, 'NOT_FOUND');
		this.name = 'NotFoundError';
	}
}

// Error handling utilities
export class ErrorHandler {
	static handle(error: unknown): { success: false; error: string; code?: string } {
		if (error instanceof DataError) {
			return {
				success: false,
				error: error.message,
				code: error.code
			};
		}

		if (error instanceof Error) {
			return {
				success: false,
				error: error.message
			};
		}

		return {
			success: false,
			error: 'An unknown error occurred'
		};
	}

	static logError(error: unknown, context?: string): void {
		const timestamp = new Date().toISOString();
		const contextStr = context ? ` [${context}]` : '';
		
		if (error instanceof Error) {
			console.error(`${timestamp}${contextStr} ${error.name}: ${error.message}`);
			if (error.stack) {
				console.error(error.stack);
			}
		} else {
			console.error(`${timestamp}${contextStr} Unknown error:`, error);
		}
	}

	static createSafeHandler<T extends (...args: any[]) => any>(
		fn: T,
		context?: string
	): (...args: Parameters<T>) => ReturnType<T> | null {
		return (...args: Parameters<T>) => {
			try {
				return fn(...args);
			} catch (error) {
				this.logError(error, context);
				return null;
			}
		};
	}
}

// Form validation utilities
export class FormValidator {
	static validateContactForm(data: any): { isValid: boolean; errors: Record<string, string> } {
		const errors: Record<string, string> = {};

		if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {
			errors.name = 'Name is required';
		} else if (data.name.trim().length > 100) {
			errors.name = 'Name must be less than 100 characters';
		}

		if (!data.email || typeof data.email !== 'string') {
			errors.email = 'Email is required';
		} else if (!DataValidator.isValidEmail(data.email)) {
			errors.email = 'Please enter a valid email address';
		}

		if (!data.subject || typeof data.subject !== 'string' || data.subject.trim().length === 0) {
			errors.subject = 'Subject is required';
		} else if (data.subject.trim().length > 200) {
			errors.subject = 'Subject must be less than 200 characters';
		}

		if (!data.message || typeof data.message !== 'string' || data.message.trim().length === 0) {
			errors.message = 'Message is required';
		} else if (data.message.trim().length > 1000) {
			errors.message = 'Message must be less than 1000 characters';
		}

		return {
			isValid: Object.keys(errors).length === 0,
			errors
		};
	}

	static sanitizeFormData(data: any): any {
		const sanitized: any = {};

		for (const [key, value] of Object.entries(data)) {
			if (typeof value === 'string') {
				sanitized[key] = DataValidator.sanitizeString(value);
			} else {
				sanitized[key] = value;
			}
		}

		return sanitized;
	}
}
