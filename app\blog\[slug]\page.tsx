import { notFound } from "next/navigation";
import Image from "next/image";
import { marked } from "marked";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { DataService } from "../../utils";

export default function BlogPost({ params }: { params: { slug: string } }) {
	const post = DataService.blog.getById(params.slug);

	if (!post) {
		notFound();
	}

	const content = marked(post.content);

	return (
		<article className="min-h-screen bg-black">
			<div className="max-w-4xl mx-auto px-6 py-20">
				{/* Back Button */}
				<Link href="/blog" className="inline-flex items-center text-zinc-400 hover:text-white mb-8">
					<ArrowLeft className="w-4 h-4 mr-2" />
					Back to Blog
				</Link>

				{/* Header Image */}
				<div className="relative h-[400px] w-full mb-8 rounded-lg overflow-hidden">
					<Image src={post.image} alt={post.title} fill className="object-cover" />
				</div>

				{/* Post Header */}
				<header className="mb-12">
					<h1 className="text-4xl font-bold text-white mb-4">{post.title}</h1>
					<div className="flex items-center gap-4 text-zinc-400 mb-6">
						<span>{post.author}</span>
						<span>•</span>
						<time dateTime={post.date}>{new Date(post.date).toLocaleDateString()}</time>
						<span>•</span>
						<span>{post.readTime}</span>
					</div>
					<div className="flex flex-wrap gap-2">
						{post.tags.map((tag) => (
							<span key={tag} className="px-3 py-1 bg-zinc-800 text-zinc-300 rounded-full text-sm">
								{tag}
							</span>
						))}
					</div>
				</header>

				{/* Post Content */}
				<div
					className="prose prose-lg prose-invert max-w-none"
					dangerouslySetInnerHTML={{ __html: content }}
				/>
			</div>
		</article>
	);
}
