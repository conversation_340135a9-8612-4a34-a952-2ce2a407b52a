"use client";
import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON>ight, Github, Linkedin, Mail, Twitter } from "lucide-react";
import { DataService, personalInfo, socialLinks, techStack, skills } from "./utils";
import Card from "./components/card";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function HomePage() {
	const router = useRouter();
	const projects = DataService.projects.getRecent(3);
	const blogPostsData = DataService.blog.getRecent(3);

	useEffect(() => {
		const routesToPrefetch = ["/projects", "/blog", "/work"];

		routesToPrefetch.forEach((route) => {
			router.prefetch(route);
		});
	}, [router]);

	return (
		<div className="min-h-screen bg-black">
			{/* Hero Section */}
			<section className="max-w-4xl mx-auto px-6 pt-16 pb-24">
				<div className="grid lg:grid-cols-2 gap-16 items-start">
					<div className="space-y-8">
						{/* Badge */}
						<div className="inline-block animate-fade-in">
							<span className="bg-blue-500/10 text-blue-400 text-xs font-medium px-3 py-1.5 rounded-full border border-blue-500/20 backdrop-blur-sm">
								Full Stack Developer
							</span>
						</div>

						{/* Main Heading */}
						<div className="space-y-6">
							<h1 className="text-4xl lg:text-5xl font-bold leading-tight text-white animate-slide-up">
								Hello, I'm{" "}
								<span className="bg-gradient-to-r from-purple-400 via-pink-400 to-purple-400 bg-clip-text text-transparent animate-pulse">
									{personalInfo.name}
								</span>{" "}
								<span className="inline-block animate-bounce">👋</span>
							</h1>

							{/* Available Status */}
							<div className="flex items-center gap-2 animate-fade-in">
								<div
									className={`w-2 h-2 rounded-full animate-pulse ${
										personalInfo.availability.status === "available"
											? "bg-green-400"
											: personalInfo.availability.status === "busy"
											? "bg-yellow-400"
											: "bg-red-400"
									}`}
								></div>
								<span className="text-sm text-zinc-400">{personalInfo.availability.message}</span>
							</div>

							{/* Description */}
							<div className="max-w-lg animate-slide-up">
								<p className="text-zinc-300 leading-relaxed font-mono text-sm">
									{personalInfo.description}
								</p>
							</div>
						</div>

						{/* CTA Buttons */}
						<div className="flex flex-wrap gap-4 animate-fade-in">
							<button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-lg text-sm font-medium flex items-center gap-2 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-blue-500/25">
								Let's work together
								<ArrowRight className="h-4 w-4" />
							</button>
						</div>

						{/* Social Links */}
						<div className="flex gap-4 pt-4 animate-fade-in">
							{socialLinks.email && (
								<Link
									href={socialLinks.email}
									className="text-zinc-500 hover:text-zinc-300 transition-all duration-300 hover:scale-110"
								>
									<Mail className="h-4 w-4" />
								</Link>
							)}
							{socialLinks.github && (
								<Link
									href={socialLinks.github}
									className="text-zinc-500 hover:text-zinc-300 transition-all duration-300 hover:scale-110"
								>
									<Github className="h-4 w-4" />
								</Link>
							)}
							{socialLinks.linkedin && (
								<Link
									href={socialLinks.linkedin}
									className="text-zinc-500 hover:text-zinc-300 transition-all duration-300 hover:scale-110"
								>
									<Linkedin className="h-4 w-4" />
								</Link>
							)}
							{socialLinks.twitter && (
								<Link
									href={socialLinks.twitter}
									className="text-zinc-500 hover:text-zinc-300 transition-all duration-300 hover:scale-110"
								>
									<Twitter className="h-4 w-4" />
								</Link>
							)}
						</div>
					</div>

					{/* Avatar */}
					<div className="flex justify-center lg:justify-end animate-fade-in">
						<div className="w-72 h-72 relative">
							<Image
								src="/placeholder.svg?height=288&width=288"
								alt="Pixel Art Avatar"
								width={288}
								height={288}
								className="w-full h-full object-contain hover:scale-105 transition-transform duration-500"
								style={{ imageRendering: "pixelated" }}
							/>
						</div>
					</div>
				</div>
			</section>

			{/* Tech Stack */}
			<section className="max-w-4xl mx-auto px-6 py-8">
				<div className="flex flex-wrap gap-3 justify-center">
					{techStack.map((tech, index) => (
						<span
							key={tech}
							className="bg-zinc-900/80 hover:bg-zinc-800/80 border border-zinc-800 hover:border-zinc-700 text-zinc-300 hover:text-white text-xs font-medium px-3 py-2 rounded-full transition-all duration-300 cursor-default hover:scale-105 backdrop-blur-sm"
							style={{
								animationDelay: `${index * 100}ms`,
							}}
						>
							{tech}
						</span>
					))}
				</div>
			</section>

			{/* Skills Section */}
			<section className="max-w-4xl mx-auto px-6 py-20">
				<h2 className="text-3xl font-bold text-white mb-12 text-center">Skills & Expertise</h2>
				<div className="grid md:grid-cols-2 gap-8">
					{skills.map((skill, index) => (
						<div
							key={skill.name}
							className="bg-zinc-900/50 p-6 rounded-xl border border-zinc-800/50 backdrop-blur-sm"
						>
							<div className="flex justify-between items-center mb-2">
								<h3 className="text-white font-medium">{skill.name}</h3>
								<span className="text-zinc-400 text-sm">{skill.level}%</span>
							</div>
							<div className="h-2 bg-zinc-800 rounded-full overflow-hidden">
								<div
									className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-1000"
									style={{ width: `${skill.level}%` }}
								/>
							</div>
						</div>
					))}
				</div>
			</section>

			{/* Projects Section */}
			<section className="max-w-4xl mx-auto px-6 py-20">
				<div className="flex justify-between items-center mb-12">
					<h2 className="text-3xl font-bold text-white">Featured Projects</h2>
					<Link
						href="/projects"
						className="text-zinc-400 hover:text-white text-sm font-medium flex items-center gap-1 transition-all duration-300 hover:scale-105"
					>
						View all
						<ArrowRight className="h-4 w-4" />
					</Link>
				</div>

				<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
					{projects.slice(0, 3).map((project, index) => (
						<Card
							key={project.id}
							type="project"
							title={project.title}
							description={project.description}
							image={project.image}
							link={project.liveUrl}
							tags={project.technologies}
							githubUrl={project.githubUrl}
							liveUrl={project.liveUrl}
							index={index}
						/>
					))}
				</div>
			</section>

			{/* Blog Section */}
			<section className="max-w-4xl mx-auto px-6 py-20">
				<div className="flex justify-between items-center mb-12">
					<h2 className="text-3xl font-bold text-white">Latest Articles</h2>
					<Link
						href="/blog"
						className="text-zinc-400 hover:text-white text-sm font-medium flex items-center gap-1 transition-all duration-300 hover:scale-105"
					>
						View all
						<ArrowRight className="h-4 w-4" />
					</Link>
				</div>

				<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
					{blogPostsData.map((post, index) => (
						<Card
							key={post.id}
							type="blog"
							title={post.title}
							description={post.description}
							image={post.image}
							link={`/blog/${post.id}`}
							tags={post.tags}
							date={post.date}
							readTime={post.readTime}
							index={index}
						/>
					))}
				</div>
			</section>

			{/* Contact Section */}
			<section className="max-w-4xl mx-auto px-6 py-20">
				<div className="bg-gradient-to-br from-zinc-900/50 to-zinc-800/30 border border-zinc-800/50 rounded-2xl p-8 backdrop-blur-sm">
					<div className="text-center mb-8">
						<h2 className="text-3xl font-bold text-white mb-4">Let's Connect</h2>
						<p className="text-zinc-400 max-w-lg mx-auto">
							I'm always open to discussing new projects, creative ideas, or opportunities to be
							part of your visions.
						</p>
					</div>

					<div className="grid md:grid-cols-3 gap-6">
						<a
							href="mailto:<EMAIL>"
							className="flex flex-col items-center p-6 bg-zinc-900/50 rounded-xl border border-zinc-800/50 hover:border-zinc-700/50 transition-all duration-300 hover:scale-105"
						>
							<Mail className="h-6 w-6 text-blue-400 mb-3" />
							<span className="text-white font-medium">Email</span>
							<span className="text-zinc-400 text-sm"><EMAIL></span>
						</a>
						<a
							href="https://linkedin.com"
							className="flex flex-col items-center p-6 bg-zinc-900/50 rounded-xl border border-zinc-800/50 hover:border-zinc-700/50 transition-all duration-300 hover:scale-105"
						>
							<Linkedin className="h-6 w-6 text-blue-400 mb-3" />
							<span className="text-white font-medium">LinkedIn</span>
							<span className="text-zinc-400 text-sm">@username</span>
						</a>
						<a
							href="https://twitter.com"
							className="flex flex-col items-center p-6 bg-zinc-900/50 rounded-xl border border-zinc-800/50 hover:border-zinc-700/50 transition-all duration-300 hover:scale-105"
						>
							<Twitter className="h-6 w-6 text-blue-400 mb-3" />
							<span className="text-white font-medium">Twitter</span>
							<span className="text-zinc-400 text-sm">@username</span>
						</a>
					</div>
				</div>
			</section>

			{/* Work Experience */}
			<section className="max-w-4xl mx-auto px-6 py-20">
				<div className="flex justify-between items-center mb-12">
					<h2 className="text-3xl font-bold text-white">Work Experience</h2>
					<Link
						href="/work"
						className="text-zinc-400 hover:text-white text-sm font-medium flex items-center gap-1 transition-all duration-300"
					>
						View all
						<ArrowRight className="h-4 w-4" />
					</Link>
				</div>

				<div className="space-y-8">
					{/* Single Experience Card */}
					<div className="bg-zinc-900/80 shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-xl p-8 flex flex-col md:flex-row md:items-center gap-8">
						{/* Company Logo/Icon (optional, placeholder for now) */}
						<div className="flex-shrink-0 flex items-center justify-center w-16 h-16 rounded-lg bg-zinc-800/80">
							<span className="text-2xl text-blue-400 font-bold">E</span>
						</div>
						{/* Details */}
						<div className="flex-1">
							<div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 mb-2">
								<div className="flex items-center gap-3">
									<h3 className="font-semibold text-xl text-white">Expelee</h3>
									<span className="text-xs px-2 py-1 rounded-full bg-zinc-800 text-zinc-400 border border-zinc-700">
										Dubai (Remote)
									</span>
								</div>
								<span className="text-xs px-2 py-1 rounded-full bg-zinc-800 text-zinc-400 border border-zinc-700">
									Mar 2024 - Present
								</span>
							</div>
							<div className="text-zinc-300 text-sm mb-2 font-medium">
								Junior Web Developer & UI/UX Designer
							</div>
							<ul className="list-disc pl-5 space-y-2 text-zinc-400 text-sm">
								<li>
									Designed and implemented scalable, responsive web applications, reducing load time
									by 20%.
								</li>
								<li>
									Created optimized user interfaces that led to a 15% increase in customer
									retention.
								</li>
								<li>
									Collaborated with cross-functional teams to improve delivery timelines by 10%.
								</li>
							</ul>
						</div>
					</div>
				</div>
			</section>
		</div>
	);
}
