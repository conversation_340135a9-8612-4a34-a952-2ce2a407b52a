"use client";

import { DataService } from "../utils";
import Card from "../components/card";

export default function ProjectsPage() {
	const projects = DataService.projects.getAll();

	return (
		<div className="min-h-screen bg-black">
			<div className="max-w-5xl mx-auto px-6 py-20">
				{/* Header */}
				<div className="mb-16 text-center">
					<h1 className="text-4xl font-bold text-white mb-4">Projects</h1>
					<p className="text-zinc-400 text-lg max-w-2xl mx-auto">
						A collection of projects showcasing my skills in web development and problem-solving.
					</p>
				</div>
				{/* Projects Grid */}
				<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
					{projects.map((project, index) => (
						<Card
							key={project.id}
							type="project"
							title={project.title}
							description={project.description}
							image={project.image}
							link={project.liveUrl}
							tags={project.technologies}
							githubUrl={project.githubUrl}
							liveUrl={project.liveUrl}
							index={index}
						/>
					))}
				</div>
			</div>
		</div>
	);
}
